import { isValidElement, cloneElement, useRef, useState, ReactElement, useCallback, RefObject } from "react";
import useChildrenDimensions from "../useChildrenDimensions";

interface RoulerConfig {
  position: 'left' | 'right' | 'top' | 'bottom';
  value: string;
}

interface WithOnChangeProps {
  onChange?: (hidden: boolean) => void;
}

interface RoulerProps {
  vertical?: RoulerConfig;
  horizontal?: RoulerConfig;
  children: React.ReactNode;
}

export default function Rouler({ vertical, horizontal, children }: RoulerProps) {
  const childRef = useRef<HTMLDivElement>(null)
  const [isAddressBarHidden, setIsAddressBarHidden] = useState(false)
  const { width, height } = useChildrenDimensions(childRef as RefObject<HTMLDivElement>)


  const renderVerticalRouler = useCallback((verticalConfig: RoulerConfig) => {
    const { position, value } = verticalConfig

    if (position === 'left') {
      return (
        <div className="flex items-center mr-2">
          <div className="text-sm">{value}</div>
          <div className="h-full px-1.5 border-t-1 border-b-1">
            <div className="h-full border-l-1"></div>
          </div>
        </div>
      )
    }

    if (position === 'right') {
      return (
        <div className="absolute -right-5 top-0 border-t-1 border-b-1 h-full w-3">
          <div className="absolute top-0 left-[50%] h-full border-l-1"></div>
          <div className="absolute top-[50%] left-[50%] translate-y-[50%] translate-x-2 text-sm">{value}</div>
        </div>
      )
    }

    return null
  }, [])

  const renderHorizontalRouler = useCallback((horizontalConfig: RoulerConfig, verticalConfig?: RoulerConfig) => {
    const { position, value } = horizontalConfig
    const { position: verticalPosition, value: verticalValue } = verticalConfig

    if (position === 'top') {
      return (
        <div className="flex">
          {verticalConfig && verticalPosition === 'left' && renderVerticalRouler(verticalConfig)}
          <div className="text-center mb-2">
            <div className="text-sm">{value}</div>
            <div className="border-r-1 border-l-1 py-1.5">
              <div className="border-t-1"></div>
            </div>
          </div>
        </div>
      )
    }

    if (position === 'bottom') {
      return (
        <div className="flex mt-2 items-start">
          {verticalConfig && verticalPosition === 'left' && (
            <div className="flex items-center mr-2 invisible">
              <div className="text-sm">{verticalValue}</div>
              <div className="px-1.5 border-t-1 border-b-1">
                <div className="border-l-1"></div>
              </div>
            </div>
          )}
          <div className="text-center flex-1">
            <div className="border-r-1 border-l-1 py-1.5">
              <div className="border-b-1"></div>
            </div>
            <div className="text-sm">{value}</div>
          </div>
        </div>
      )
    }

    return null
  }, [])

  const handleAddressBarChange = useCallback((hidden: boolean) => setIsAddressBarHidden(hidden), [])

  const renderChildren = useCallback(() => {
    if (!isValidElement(children)) { return children }

    const childElement = children as ReactElement<WithOnChangeProps & { ref?: React.Ref<HTMLDivElement> }>;

    const childWithRef = typeof childElement.type !== 'string' && !childElement.props.onChange
      ? cloneElement(childElement, {
        ref: childRef,
        onChange: handleAddressBarChange
      })
      : cloneElement(childElement, { ref: childRef });

    return childWithRef;
  }, [children, handleAddressBarChange]);

  return (
    <div className="">
      <div className="inline-flex flex-col">
        {horizontal && horizontal.position === 'top' && renderHorizontalRouler(horizontal, vertical)}
        <div className="inline-flex">
          {vertical && vertical.position === 'left' && renderVerticalRouler(vertical)}
          {renderChildren()}
          {vertical && vertical.position === 'right' && renderVerticalRouler(vertical)}
        </div>
        {horizontal && horizontal.position === 'bottom' && renderHorizontalRouler(horizontal, vertical)}
      </div>
    </div>
  )
}
